import os
import sys
import shutil
import random
import threading
import math
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from concurrent.futures import ThreadPoolExecutor
import requests
import webbrowser
from PIL import Image, ImageTk, ImageDraw, ImageFont, ImageOps
import numpy as np
from moviepy.editor import (
    VideoFileClip, AudioFileClip, TextClip,
    CompositeVideoClip, concatenate_videoclips,
    ImageClip, ColorClip, VideoClip
)
import moviepy.video.fx.all as vfx

# ----------------------------- إعدادات متقدمة -----------------------------
# تحديد المسارات للبرامج المساعدة


# دالة البحث عن ffmpeg و ImageMagick

def find_binary(filename, default_path):
    exe_dir = os.path.dirname(sys.executable) if getattr(sys, "frozen", False) else os.path.dirname(__file__)
    local_path = os.path.join(exe_dir, filename)
    return local_path if os.path.exists(local_path) else default_path


ffmpeg_path = find_binary("ffmpeg.exe", r"C:\ffmpeg\ffmpeg.exe")
magick_path = find_binary("magick.exe", r"C:\Program Files\ImageMagick\magick.exe")

import moviepy.config as cf
cf.IMAGEMAGICK_BINARY = magick_path
cf.FFMPEG_BINARY = ffmpeg_path
cf.CACHE_SIZE = 2048  # 2GB cache لتحسين الأداء

# تحديث متغيرات البيئة
os.environ["PATH"] += os.pathsep + os.path.dirname(ffmpeg_path)
os.environ["FFMPEG_BINARY"] = ffmpeg_path
os.environ["IMAGEIO_FFMPEG_EXE"] = ffmpeg_path
os.environ["IMAGEMAGICK_BINARY"] = magick_path

try:
    if not os.path.exists(ffmpeg_path):
        raise FileNotFoundError(f"ffmpeg.exe not found at: {ffmpeg_path}")
    if not os.path.exists(magick_path):
        raise FileNotFoundError(f"magick.exe not found at: {magick_path}")

    from pydub import AudioSegment

    AudioSegment.converter = ffmpeg_path
    AudioSegment.ffmpeg = ffmpeg_path
    AudioSegment.ffprobe = ffmpeg_path

    from moviepy.editor import (
        VideoFileClip, AudioFileClip,
        TextClip, CompositeVideoClip,
        concatenate_videoclips, ImageClip
    )
    import moviepy.video.fx.all as vfx

except FileNotFoundError as e:
    messagebox.showerror("خطأ في التهيئة", f"الملفات التنفيذية لـ FFmpeg أو ImageMagick مفقودة. تأكد من وجودهما بجانب البرنامج أو في المسارات المحددة.
التفاصيل: {e}")
    sys.exit(1)
except Exception as e:
    messagebox.showerror("خطأ في التهيئة", f"فشل إعداد المكتبات (PyDub/MoviePy). تأكد من تثبيتها بشكل صحيح ووجود FFmpeg/ImageMagick.
التفاصيل: {e}")
    sys.exit(1)

# ضبط عدد الخيوط
OPTIMAL_THREADS = max(1, os.cpu_count() // 2)

# كشف نوع GPU (مبسّط - عدل حسب احتياجك)
GPU_TYPE = 'nvidia'  # ممكن تغيرها لـ 'unknown' إذا لا تستخدم GPU

# إعدادات ترميز فيديو سريع مع تسريع عتادي لـ NVIDIA
FAST_VIDEO_ENCODING_SETTINGS = {
    'threads': OPTIMAL_THREADS,
    'fps': 30,
    'preset': 'ultrafast',
    'codec': 'libx264',
    'audio_codec': 'aac',
    'audio_bitrate': '128k',
    'bitrate': '1500k',
    'write_logfile': False,
    'temp_audiofile': False,
    'remove_temp': True,
    'ffmpeg_params': [
        '-movflags', 'faststart',
        '-profile:v', 'main',
        '-level', '4.0',
        '-pix_fmt', 'yuv420p',
        '-crf', '28',
        '-bufsize', '1500k',
        '-maxrate', '2000k',
        '-x264-params', 'ref=1:bframes=0:me=dia:subme=1:trellis=0:aq-mode=0'
    ]
}

if GPU_TYPE == 'nvidia':
    FAST_VIDEO_ENCODING_SETTINGS['codec'] = 'h264_nvenc'
    FAST_VIDEO_ENCODING_SETTINGS.pop('preset', None)

# ----------------------------- تحميل الصوتيات وتسريعها -----------------------------

def download_audio(reciter_id, surah, ayah, idx):
    """تحميل ملف صوتي للآية - نسخة محسنة من كودك الأصلي."""
    os.makedirs("audio", exist_ok=True)
    fn = f"{surah:03d}{ayah:03d}.mp3"
    out = os.path.join("audio", f"part{idx}.mp3")

    # هنا ضمّنت النسخة الأساسية من تحميل الصوت مع المحاولات المتعددة (اختصرت للكثافة)
    base_url = f"https://everyayah.com/data/AbdulSamad_64kbps_QuranExplorer.Com/{fn}"

    try:
        import requests
        headers = {
            'User-Agent': 'Mozilla/5.0',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        }
        r = requests.get(base_url, timeout=10, headers=headers)
        r.raise_for_status()
        if len(r.content) > 1024:
            with open(out, 'wb') as f:
                f.write(r.content)
            return out
    except Exception as e:
        print(f"Failed to download {fn}: {e}")

    # إنشاء ملف صوتي فارغ بديل
    from pydub import AudioSegment
    silent_audio = AudioSegment.silent(duration=5000)  # 5 seconds silence
    silent_audio.export(out, format='mp3')
    return out


def download_audio_parallel(reciter_id, surah, start_ayah, end_ayah):
    """تحميل الصوتيات بشكل متوازي لتسريع العملية."""
    audio_files = [None] * (end_ayah - start_ayah + 1)

    def task(idx, ayah):
        return idx, download_audio(reciter_id, surah, ayah, idx)

    with ThreadPoolExecutor(max_workers=OPTIMAL_THREADS) as executor:
        futures = [executor.submit(task, i, ayah) for i, ayah in enumerate(range(start_ayah, end_ayah + 1))]
        for future in futures:
            idx, file_path = future.result()
            audio_files[idx] = file_path

    return audio_files


def merge_audio_remove_silence(audio_files):
    """دمج الصوت مع إزالة صمت خفيف بين الآيات."""
    combined = AudioSegment.empty()
    for file in audio_files:
        audio = AudioSegment.from_file(file)
        trimmed = audio.strip_silence(silence_len=150, silence_thresh=-40)
        combined += trimmed
    return combined


def create_fast_video(video_path, audio_segment, output_path):
    """إنشاء فيديو مع صوت مدمج وتسريع الترميز."""
    video_clip = VideoFileClip(video_path)

    temp_audio_path = "temp_audio.mp3"
    audio_segment.export(temp_audio_path, format="mp3")

    video_clip = video_clip.set_audio(AudioSegment.from_file(temp_audio_path))

    params = FAST_VIDEO_ENCODING_SETTINGS.copy()
    video_clip.write_videofile(
        output_path,
        threads=params['threads'],
        fps=params['fps'],
        codec=params['codec'],
        audio_codec=params['audio_codec'],
        bitrate=params['bitrate'],
        audio_bitrate=params['audio_bitrate'],
        ffmpeg_params=params.get('ffmpeg_params', [])
    )

    if os.path.exists(temp_audio_path):
        os.remove(temp_audio_path)


# ----------------------------- مثال تشغيل -----------------------------
if __name__ == '__main__':
    reciter = "AbdulSamad_64kbps_QuranExplorer.Com"
    surah_num = 1
    start_ayah = 1
    end_ayah = 7

    print("تحميل الصوتيات...")
    audio_files = download_audio_parallel(reciter, surah_num, start_ayah, end_ayah)

    print("دمج الصوتيات مع إزالة الفواصل الصوتية...")
    combined_audio = merge_audio_remove_silence(audio_files)

    print("إنشاء الفيديو بسرعة...")
    background_video_path = "background.mp4"  # استبدل بالمسار الصحيح
    output_video_path = "final_video.mp4"

    create_fast_video(background_video_path, combined_audio, output_video_path)

    print("تم إنشاء الفيديو بنجاح وبسرعة عالية مع إزالة الفواصل الصوتية.")
